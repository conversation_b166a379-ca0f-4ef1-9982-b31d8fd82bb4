/**
 * Entity Type Filters Utility for Frontend
 * 
 * This utility converts frontend entity type display names to backend-expected keys
 * and provides type definitions for all available filters.
 */

// Entity Type Mapping: Frontend Display Name -> Backend Key
export const ENTITY_TYPE_MAPPING: Record<string, string> = {
  'AI Tool': 'tool',
  'Course': 'course',
  'Job': 'job',
  'Hardware': 'hardware',
  'Event': 'event',
  'Agency': 'agency',
  'Software': 'software',
  'Research Paper': 'research_paper',
  'Podcast': 'podcast',
  'Community': 'community',
  'Grant': 'grant',
  'Newsletter': 'newsletter',
  'Book': 'book',
  'Platform': 'software', // Platform maps to Software
  'API': 'tool', // API maps to Tool
  'Model': 'tool', // Model maps to Tool
  'Dataset': 'tool', // Dataset maps to Tool
  'Library': 'tool', // Library maps to Tool
  'Service': 'tool', // Service maps to Tool
  'Tool': 'tool', // Tool maps to tool
};

// Reverse mapping for display purposes
export const BACKEND_TO_DISPLAY_MAPPING: Record<string, string> = Object.fromEntries(
  Object.entries(ENTITY_TYPE_MAPPING).map(([display, backend]) => [backend, display])
);

// Type definitions for each entity type filter
export interface ToolFilters {
  technical_levels?: ('BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT')[];
  learning_curves?: ('EASY' | 'MODERATE' | 'STEEP')[];
  pricing_models?: ('FREE' | 'FREEMIUM' | 'SUBSCRIPTION' | 'ONE_TIME')[];
  price_ranges?: ('FREE' | 'UNDER_10' | 'UNDER_50' | 'UNDER_100' | 'OVER_100')[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  platforms?: string[];
  integrations?: string[];
  frameworks?: string[];
  libraries?: string[];
  key_features_search?: string;
  use_cases_search?: string;
  target_audience_search?: string;
  deployment_options?: string[];
  support_channels?: string[];
  has_live_chat?: boolean;
  customization_level?: string;
  pricing_details_search?: string;
}

export interface CourseFilters {
  skill_levels?: ('BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT')[];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;
  has_syllabus?: boolean;
}

export interface JobFilters {
  employment_types?: string[];
  experience_levels?: string[];
  location_types?: string[];
  company_name?: string;
  job_title?: string;
  salary_min?: number; // in thousands
  salary_max?: number; // in thousands
  job_description?: string;
  has_application_url?: boolean;
}

export interface HardwareFilters {
  hardware_types?: string[];
  manufacturers?: string[];
  release_date_from?: string;
  release_date_to?: string;
  price_min?: number;
  price_max?: number;
  specifications_search?: string;
  has_datasheet?: boolean;
  memory_search?: string;
  processor_search?: string;
}

export interface EventFilters {
  event_types?: string[];
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  registration_required?: boolean;
  has_registration_url?: boolean;
  speakers_search?: string;
  target_audience_search?: string;
}

// Union type for all possible filters
export type EntityTypeFilters = {
  tool?: ToolFilters;
  course?: CourseFilters;
  job?: JobFilters;
  hardware?: HardwareFilters;
  event?: EventFilters;
  agency?: Record<string, unknown>; // Add specific type if needed
  software?: Record<string, unknown>; // Add specific type if needed
  research_paper?: Record<string, unknown>; // Add specific type if needed
  podcast?: Record<string, unknown>; // Add specific type if needed
  community?: Record<string, unknown>; // Add specific type if needed
  grant?: Record<string, unknown>; // Add specific type if needed
  newsletter?: Record<string, unknown>; // Add specific type if needed
  book?: Record<string, unknown>; // Add specific type if needed
};

/**
 * Converts frontend entity type filters to backend format
 *
 * @param frontendFilters - Object with display names as keys
 * @returns Object with backend keys
 */
export function convertEntityTypeFilters(
  frontendFilters: Record<string, unknown>
): EntityTypeFilters {
  const backendFilters: EntityTypeFilters = {};

  for (const [displayName, filters] of Object.entries(frontendFilters)) {
    const backendKey = ENTITY_TYPE_MAPPING[displayName];
    if (backendKey && filters && Object.keys(filters).length > 0) {
      (backendFilters as Record<string, unknown>)[backendKey] = filters;
    }
  }

  return backendFilters;
}

/**
 * Validates that entity type filters have the correct structure
 *
 * @param filters - Filters to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateEntityTypeFilters(filters: Record<string, unknown>): string[] {
  const errors: string[] = [];

  for (const [displayName, filterObj] of Object.entries(filters)) {
    if (!ENTITY_TYPE_MAPPING[displayName]) {
      errors.push(`Unknown entity type: "${displayName}". Valid types: ${Object.keys(ENTITY_TYPE_MAPPING).join(', ')}`);
      continue;
    }

    if (typeof filterObj !== 'object' || filterObj === null) {
      errors.push(`Filters for "${displayName}" must be an object`);
      continue;
    }

    // Validate specific entity type filters
    const entityErrors = validateSpecificEntityFilters(displayName, filterObj);
    errors.push(...entityErrors);
  }

  return errors;
}

// Type for entity filters
type EntityFilters = Record<string, unknown>;

/**
 * Validates filters for specific entity types with comprehensive checks
 */
function validateSpecificEntityFilters(entityType: string, filters: EntityFilters): string[] {
  switch (entityType) {
    case 'AI Tool':
      return validateToolFilters(filters);
    case 'Course':
      return validateCourseFilters(filters);
    case 'Job':
      return validateJobFilters(filters);
    case 'Hardware':
      return validateHardwareFilters(filters);
    case 'Event':
      return validateEventFilters(filters);
    default:
      // For other entity types, do basic validation
      return validateGenericFilters(entityType, filters);
  }
}

/**
 * Validates AI Tool filters
 */
function validateToolFilters(filters: EntityFilters): string[] {
  const errors: string[] = [];

  if (filters.technical_levels) {
    const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
    const invalidLevels = filters.technical_levels.filter((level: string) => !validLevels.includes(level));
    if (invalidLevels.length > 0) {
      errors.push(`Invalid technical_levels: ${invalidLevels.join(', ')}. Valid: ${validLevels.join(', ')}`);
    }
  }

  if (filters.learning_curves) {
    const validCurves = ['EASY', 'MODERATE', 'STEEP'];
    const invalidCurves = filters.learning_curves.filter((curve: string) => !validCurves.includes(curve));
    if (invalidCurves.length > 0) {
      errors.push(`Invalid learning_curves: ${invalidCurves.join(', ')}. Valid: ${validCurves.join(', ')}`);
    }
  }

  if (filters.pricing_models) {
    const validModels = ['FREE', 'FREEMIUM', 'SUBSCRIPTION', 'ONE_TIME'];
    const invalidModels = filters.pricing_models.filter((model: string) => !validModels.includes(model));
    if (invalidModels.length > 0) {
      errors.push(`Invalid pricing_models: ${invalidModels.join(', ')}. Valid: ${validModels.join(', ')}`);
    }
  }

  if (filters.price_ranges) {
    const validRanges = ['FREE', 'UNDER_10', 'UNDER_50', 'UNDER_100', 'OVER_100'];
    const invalidRanges = filters.price_ranges.filter((range: string) => !validRanges.includes(range));
    if (invalidRanges.length > 0) {
      errors.push(`Invalid price_ranges: ${invalidRanges.join(', ')}. Valid: ${validRanges.join(', ')}`);
    }
  }

  // Validate boolean fields
  const booleanFields = ['has_api', 'has_free_tier', 'open_source', 'mobile_support', 'demo_available', 'has_live_chat'];
  booleanFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'boolean') {
      errors.push(`${field} must be a boolean value`);
    }
  });

  // Validate array fields
  const arrayFields = ['platforms', 'integrations', 'frameworks', 'libraries', 'deployment_options', 'support_channels'];
  arrayFields.forEach(field => {
    if (filters[field] !== undefined && !Array.isArray(filters[field])) {
      errors.push(`${field} must be an array`);
    }
  });

  // Validate string fields
  const stringFields = ['key_features_search', 'use_cases_search', 'target_audience_search', 'customization_level', 'pricing_details_search'];
  stringFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  return errors;
}

/**
 * Validates Course filters
 */
function validateCourseFilters(filters: EntityFilters): string[] {
  const errors: string[] = [];

  if (filters.skill_levels) {
    const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
    const invalidLevels = filters.skill_levels.filter((level: string) => !validLevels.includes(level));
    if (invalidLevels.length > 0) {
      errors.push(`Invalid skill_levels: ${invalidLevels.join(', ')}. Valid: ${validLevels.join(', ')}`);
    }
  }

  // Validate enrollment range
  if (filters.enrollment_min !== undefined && filters.enrollment_max !== undefined) {
    if (filters.enrollment_min > filters.enrollment_max) {
      errors.push('enrollment_min cannot be greater than enrollment_max');
    }
  }

  // Validate numeric fields
  const numericFields = ['enrollment_min', 'enrollment_max'];
  numericFields.forEach(field => {
    if (filters[field] !== undefined) {
      if (typeof filters[field] !== 'number' || filters[field] < 0) {
        errors.push(`${field} must be a positive number`);
      }
    }
  });

  // Validate boolean fields
  const booleanFields = ['certificate_available', 'has_syllabus'];
  booleanFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'boolean') {
      errors.push(`${field} must be a boolean value`);
    }
  });

  // Validate string fields
  const stringFields = ['instructor_name', 'duration_text', 'prerequisites'];
  stringFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  return errors;
}

/**
 * Validates Job filters
 */
function validateJobFilters(filters: EntityFilters): string[] {
  const errors: string[] = [];

  // Validate salary range
  if (filters.salary_min !== undefined && filters.salary_max !== undefined) {
    if (filters.salary_min > filters.salary_max) {
      errors.push('salary_min cannot be greater than salary_max');
    }
  }

  // Validate numeric fields (salary in thousands)
  const numericFields = ['salary_min', 'salary_max'];
  numericFields.forEach(field => {
    if (filters[field] !== undefined) {
      if (typeof filters[field] !== 'number' || filters[field] < 0) {
        errors.push(`${field} must be a positive number (in thousands)`);
      }
    }
  });

  // Validate array fields
  const arrayFields = ['employment_types', 'experience_levels', 'location_types'];
  arrayFields.forEach(field => {
    if (filters[field] !== undefined && !Array.isArray(filters[field])) {
      errors.push(`${field} must be an array`);
    }
  });

  // Validate boolean fields
  if (filters.has_application_url !== undefined && typeof filters.has_application_url !== 'boolean') {
    errors.push('has_application_url must be a boolean value');
  }

  // Validate string fields
  const stringFields = ['company_name', 'job_title', 'job_description'];
  stringFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  return errors;
}

/**
 * Validates Hardware filters
 */
function validateHardwareFilters(filters: EntityFilters): string[] {
  const errors: string[] = [];

  // Validate date range
  if (filters.release_date_from && filters.release_date_to) {
    const fromDate = new Date(filters.release_date_from);
    const toDate = new Date(filters.release_date_to);
    if (fromDate > toDate) {
      errors.push('release_date_from cannot be after release_date_to');
    }
  }

  // Validate price range
  if (filters.price_min !== undefined && filters.price_max !== undefined) {
    if (filters.price_min > filters.price_max) {
      errors.push('price_min cannot be greater than price_max');
    }
  }

  // Validate numeric fields
  const numericFields = ['price_min', 'price_max'];
  numericFields.forEach(field => {
    if (filters[field] !== undefined) {
      if (typeof filters[field] !== 'number' || filters[field] < 0) {
        errors.push(`${field} must be a positive number`);
      }
    }
  });

  // Validate date fields
  const dateFields = ['release_date_from', 'release_date_to'];
  dateFields.forEach(field => {
    if (filters[field] !== undefined) {
      const date = new Date(filters[field]);
      if (isNaN(date.getTime())) {
        errors.push(`${field} must be a valid date (YYYY-MM-DD format)`);
      }
    }
  });

  // Validate array fields
  const arrayFields = ['hardware_types', 'manufacturers'];
  arrayFields.forEach(field => {
    if (filters[field] !== undefined && !Array.isArray(filters[field])) {
      errors.push(`${field} must be an array`);
    }
  });

  // Validate boolean fields
  if (filters.has_datasheet !== undefined && typeof filters.has_datasheet !== 'boolean') {
    errors.push('has_datasheet must be a boolean value');
  }

  // Validate string fields
  const stringFields = ['specifications_search', 'memory_search', 'processor_search'];
  stringFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  return errors;
}

/**
 * Validates Event filters
 */
function validateEventFilters(filters: EntityFilters): string[] {
  const errors: string[] = [];

  // Validate date ranges
  if (filters.start_date_from && filters.start_date_to) {
    const fromDate = new Date(filters.start_date_from);
    const toDate = new Date(filters.start_date_to);
    if (fromDate > toDate) {
      errors.push('start_date_from cannot be after start_date_to');
    }
  }

  if (filters.end_date_from && filters.end_date_to) {
    const fromDate = new Date(filters.end_date_from);
    const toDate = new Date(filters.end_date_to);
    if (fromDate > toDate) {
      errors.push('end_date_from cannot be after end_date_to');
    }
  }

  // Validate date fields
  const dateFields = ['start_date_from', 'start_date_to', 'end_date_from', 'end_date_to'];
  dateFields.forEach(field => {
    if (filters[field] !== undefined) {
      const date = new Date(filters[field]);
      if (isNaN(date.getTime())) {
        errors.push(`${field} must be a valid date (YYYY-MM-DD format)`);
      }
    }
  });

  // Validate array fields
  if (filters.event_types !== undefined && !Array.isArray(filters.event_types)) {
    errors.push('event_types must be an array');
  }

  // Validate boolean fields
  const booleanFields = ['is_online', 'registration_required', 'has_registration_url'];
  booleanFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'boolean') {
      errors.push(`${field} must be a boolean value`);
    }
  });

  // Validate string fields
  const stringFields = ['location', 'price_text', 'speakers_search', 'target_audience_search'];
  stringFields.forEach(field => {
    if (filters[field] !== undefined && typeof filters[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  return errors;
}

/**
 * Validates generic filters for entity types without specific validation
 */
function validateGenericFilters(entityType: string, filters: EntityFilters): string[] {
  const errors: string[] = [];

  // Basic validation for unknown entity types
  Object.entries(filters).forEach(([key, value]) => {
    if (value === null || value === undefined) {
      return; // Skip null/undefined values
    }

    // Check for common patterns
    if (key.includes('_min') || key.includes('_max')) {
      if (typeof value !== 'number' || value < 0) {
        errors.push(`${key} must be a positive number`);
      }
    }

    if (key.includes('date') && typeof value === 'string') {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        errors.push(`${key} must be a valid date`);
      }
    }

    if (key.includes('has_') || key.includes('is_') || key.includes('_available')) {
      if (typeof value !== 'boolean') {
        errors.push(`${key} must be a boolean value`);
      }
    }
  });

  return errors;
}

/**
 * Validates and sanitizes entity type filters, removing invalid entries
 */
export function sanitizeEntityTypeFilters(filters: Record<string, EntityFilters>): {
  sanitized: Record<string, EntityFilters>;
  errors: string[];
} {
  const sanitized: Record<string, EntityFilters> = {};
  const errors: string[] = [];

  for (const [displayName, filterObj] of Object.entries(filters)) {
    if (!ENTITY_TYPE_MAPPING[displayName]) {
      errors.push(`Removed unknown entity type: "${displayName}"`);
      continue;
    }

    if (typeof filterObj !== 'object' || filterObj === null) {
      errors.push(`Removed invalid filters for "${displayName}" (not an object)`);
      continue;
    }

    // Clean empty filters
    const cleanedFilters: EntityFilters = {};
    Object.entries(filterObj).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '' &&
          (!Array.isArray(value) || value.length > 0)) {
        cleanedFilters[key] = value;
      }
    });

    if (Object.keys(cleanedFilters).length > 0) {
      sanitized[displayName] = cleanedFilters;
    }
  }

  return { sanitized, errors };
}

export const entityTypeFiltersUtils = {
  ENTITY_TYPE_MAPPING,
  BACKEND_TO_DISPLAY_MAPPING,
  convertEntityTypeFilters,
  validateEntityTypeFilters,
  sanitizeEntityTypeFilters,
};
