import { Entity } from './entity';

/**
 * Represents a single message in the chat conversation
 */
export interface ChatMessage {
  /** Unique identifier for the message */
  id: string;
  /** The text content of the message */
  text: string;
  /** Who sent the message - user or AI */
  sender: 'user' | 'ai';
  /** Timestamp when the message was created */
  timestamp: Date;
  /** Optional recommended entities if the AI message includes recommendations */
  recommendedEntities?: Entity[];
  /** Optional metadata for the message */
  metadata?: {
    /** Whether this message is still being processed */
    isLoading?: boolean;
    /** Any error that occurred while processing this message */
    error?: string;
    /** Additional context or data */
    [key: string]: unknown;
  };
}

/**
 * Request payload for the chat API endpoint
 */
export interface ChatRequest {
  /** The complete conversation history including the new user message */
  messages: ChatMessage[];
  /** Optional context or preferences for the AI response */
  context?: {
    /** User preferences for response style */
    responseStyle?: 'detailed' | 'concise' | 'technical';
    /** Maximum number of entities to recommend */
    maxRecommendations?: number;
    /** Filters to apply to entity recommendations */
    filters?: {
      entityTypes?: string[];
      categories?: string[];
      tags?: string[];
      features?: string[];
      priceRange?: string[];
      [key: string]: unknown;
    };
  };
}

/**
 * Response from the chat API endpoint
 */
export interface ChatResponse {
  /** The AI's response message */
  message: ChatMessage;
  /** Whether the conversation should continue */
  shouldContinue?: boolean;
  /** Optional metadata about the response */
  metadata?: {
    /** Processing time for the request */
    processingTime?: number;
    /** Confidence score for the response */
    confidence?: number;
    /** Additional context */
    [key: string]: unknown;
  };
}

/**
 * Error response from the chat API
 */
export interface ChatError {
  /** Error message */
  message: string;
  /** Error code for programmatic handling */
  code?: string;
  /** Additional error details */
  details?: string;
  /** HTTP status code */
  statusCode?: number;
}

/**
 * State interface for managing the chat conversation
 */
export interface ChatState {
  /** Array of all messages in the conversation */
  messages: ChatMessage[];
  /** Current user input */
  userInput: string;
  /** Whether a request is currently being processed */
  isLoading: boolean;
  /** Any error that occurred */
  error: string | null;
  /** Whether the chat has been initialized */
  isInitialized: boolean;
}

/**
 * Actions for updating chat state
 */
export type ChatAction =
  | { type: 'SET_USER_INPUT'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'INITIALIZE_CHAT' }
  | { type: 'CLEAR_CONVERSATION' };

/**
 * Utility type for creating a new chat message
 */
export interface CreateMessageOptions {
  text: string;
  sender: 'user' | 'ai';
  recommendedEntities?: Entity[];
  metadata?: ChatMessage['metadata'];
}
