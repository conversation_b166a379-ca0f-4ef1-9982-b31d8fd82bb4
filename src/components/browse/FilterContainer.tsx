'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Filter } from 'lucide-react';
import SmartFilterSection from './SmartFilterSection';
import ComprehensiveFilters from './ComprehensiveFilters';

interface FilterContainerProps {
  // Basic filter data
  allEntityTypes: Array<{ id: string; name: string }>;
  allCategories: Array<{ id: string; name: string }>;
  allFeatures: Array<{ id: string; name: string }>;
  allTags: Array<{ id: string; name: string }>;

  // Selected filter IDs
  selectedEntityTypeIds: string[];
  selectedCategoryIds: string[];
  selectedFeatureIds: string[];
  selectedTagIds: string[];

  // Advanced filter values
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  createdAtFrom?: string;
  createdAtTo?: string;
  locationSearch?: string;
  ratingMin?: number;
  ratingMax?: number;
  reviewCountMin?: number;
  reviewCountMax?: number;
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  hasAffiliateLink?: boolean;
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  status?: string;

  // Entity-specific filters (flat parameters from URL)
  currentEntityFilters?: Record<string, unknown>;

  // Loading states
  isLoadingFilters?: boolean;

  // Event handlers
  onEntityTypeToggle: (id: string) => void;
  onCategoryToggle: (id: string) => void;
  onFeatureToggle: (id: string) => void;
  onTagToggle: (id: string) => void;
  onAdvancedFilterChange: (filterName: string, value: unknown) => void;
  onClearAdvancedFilters: () => void;
}

const FilterContainer: React.FC<FilterContainerProps> = ({
  // Basic filter data
  allEntityTypes,
  allCategories,
  allFeatures,
  allTags,

  // Selected filter IDs
  selectedEntityTypeIds,
  selectedCategoryIds,
  selectedFeatureIds,
  selectedTagIds,

  // Advanced filter values
  hasFreeTier,
  apiAccess,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  createdAtFrom,
  createdAtTo,
  locationSearch,
  ratingMin,
  ratingMax,
  reviewCountMin,
  reviewCountMax,
  affiliateStatus,
  hasAffiliateLink,
  integrations,
  platforms,
  targetAudience,
  status,

  // Entity-specific filters
  currentEntityFilters = {},

  // Loading states
  isLoadingFilters = false,

  // Event handlers
  onEntityTypeToggle,
  onCategoryToggle,
  onFeatureToggle,
  onTagToggle,
  onAdvancedFilterChange,
  onClearAdvancedFilters,
}) => {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Calculate total active filters for mobile badge
  const totalActiveFilters = 
    selectedEntityTypeIds.length + 
    selectedCategoryIds.length + 
    selectedFeatureIds.length + 
    selectedTagIds.length;

  // Common filter sections component
  const FilterSections = () => (
    <div className="space-y-6">
      <SmartFilterSection
        title="Resource Type"
        items={allEntityTypes}
        selectedIds={selectedEntityTypeIds}
        onToggle={onEntityTypeToggle}
        isLoading={isLoadingFilters}
        defaultExpanded={true}
      />

      <SmartFilterSection
        title="Category"
        items={allCategories}
        selectedIds={selectedCategoryIds}
        onToggle={onCategoryToggle}
        isLoading={isLoadingFilters}
      />

      <SmartFilterSection
        title="Features"
        items={allFeatures}
        selectedIds={selectedFeatureIds}
        onToggle={onFeatureToggle}
        isLoading={isLoadingFilters}
      />

      <SmartFilterSection
        title="Tags"
        items={allTags}
        selectedIds={selectedTagIds}
        onToggle={onTagToggle}
        isLoading={isLoadingFilters}
      />

      <ComprehensiveFilters
        hasFreeTier={hasFreeTier}
        apiAccess={apiAccess}
        employeeCountRanges={employeeCountRanges}
        fundingStages={fundingStages}
        pricingModels={pricingModels}
        priceRanges={priceRanges}
        createdAtFrom={createdAtFrom}
        createdAtTo={createdAtTo}
        locationSearch={locationSearch}
        ratingMin={ratingMin}
        ratingMax={ratingMax}
        reviewCountMin={reviewCountMin}
        reviewCountMax={reviewCountMax}
        affiliateStatus={affiliateStatus}
        hasAffiliateLink={hasAffiliateLink}
        integrations={integrations}
        platforms={platforms}
        targetAudience={targetAudience}
        status={status}
        selectedEntityTypes={selectedEntityTypeIds}
        allEntityTypes={allEntityTypes}
        currentEntityFilters={currentEntityFilters}
        isLoading={isLoadingFilters}
        onFilterChange={onAdvancedFilterChange}
        onClearAdvanced={onClearAdvancedFilters}
      />
    </div>
  );

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-6">
        <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="inline-flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filters
              {totalActiveFilters > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {totalActiveFilters}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[320px] overflow-y-auto">
            <div className="space-y-6 pt-6">
              <FilterSections />
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Filter Sidebar */}
      <aside className="hidden lg:block w-80 flex-shrink-0">
        <div className="sticky top-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Filters</h2>
          <FilterSections />
        </div>
      </aside>

      {/* Overlay for mobile when filter is open */}
      {isMobileFilterOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 lg:hidden"
          onClick={() => setIsMobileFilterOpen(false)}
        />
      )}
    </>
  );
};

export default FilterContainer;
