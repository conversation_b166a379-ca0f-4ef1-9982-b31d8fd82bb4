'use client';

import React from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { CreateEntityDto } from '@/types/entity';

interface JobDetailsFormProps {
  register: UseFormRegister<CreateEntityDto>;
  errors: FieldErrors<CreateEntityDto>;
  setValue: UseFormSetValue<CreateEntityDto>;
  watch: UseFormWatch<CreateEntityDto>;
}

export default function JobDetailsForm({ register, errors, setValue, watch }: JobDetailsFormProps) {
  const isRemote = watch('details.is_remote');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Job Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Company Name */}
        <div>
          <Label htmlFor="company_name">Company Name</Label>
          <Input
            id="company_name"
            {...register('details.company_name')}
            placeholder="OpenAI, Google, Microsoft..."
            className="mt-1"
          />
        </div>

        {/* Job Type */}
        <div>
          <Label htmlFor="job_type">Job Type</Label>
          <Select onValueChange={(value) => setValue('details.job_type', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select job type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Full-time">Full-time</SelectItem>
              <SelectItem value="Part-time">Part-time</SelectItem>
              <SelectItem value="Contract">Contract</SelectItem>
              <SelectItem value="Internship">Internship</SelectItem>
              <SelectItem value="Freelance">Freelance</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Experience Level */}
        <div>
          <Label htmlFor="experience_level">Experience Level</Label>
          <Select onValueChange={(value) => setValue('details.experience_level', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select experience level..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Entry Level">Entry Level</SelectItem>
              <SelectItem value="Mid Level">Mid Level</SelectItem>
              <SelectItem value="Senior Level">Senior Level</SelectItem>
              <SelectItem value="Lead/Principal">Lead/Principal</SelectItem>
              <SelectItem value="Executive">Executive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Location */}
        <div>
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            {...register('details.location')}
            placeholder="San Francisco, CA | New York, NY | Remote"
            className="mt-1"
          />
        </div>

        {/* Salary Min */}
        <div>
          <Label htmlFor="salary_min">Salary Min (USD)</Label>
          <Input
            id="salary_min"
            type="number"
            {...register('details.salary_min', { valueAsNumber: true })}
            placeholder="80000"
            className="mt-1"
          />
        </div>

        {/* Salary Max */}
        <div>
          <Label htmlFor="salary_max">Salary Max (USD)</Label>
          <Input
            id="salary_max"
            type="number"
            {...register('details.salary_max', { valueAsNumber: true })}
            placeholder="150000"
            className="mt-1"
          />
        </div>

        {/* Application URL */}
        <div className="md:col-span-2">
          <Label htmlFor="application_url">Application URL</Label>
          <Input
            id="application_url"
            type="url"
            {...register('details.application_url')}
            placeholder="https://company.com/careers/job-id"
            className="mt-1"
          />
        </div>
      </div>

      {/* Remote Work */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="is_remote"
          checked={isRemote}
          onCheckedChange={(checked) => setValue('details.is_remote', checked)}
        />
        <Label htmlFor="is_remote">Remote Work Available</Label>
      </div>

      {/* Key Responsibilities */}
      <div>
        <Label htmlFor="key_responsibilities">Key Responsibilities (comma-separated)</Label>
        <Textarea
          id="key_responsibilities"
          {...register('details.key_responsibilities_text')}
          placeholder="Develop ML models, Design AI systems, Lead research projects..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter responsibilities separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Required Skills */}
      <div>
        <Label htmlFor="required_skills">Required Skills (comma-separated)</Label>
        <Textarea
          id="required_skills"
          {...register('details.required_skills_text')}
          placeholder="Python, TensorFlow, PyTorch, Machine Learning, Deep Learning..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter required skills separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
