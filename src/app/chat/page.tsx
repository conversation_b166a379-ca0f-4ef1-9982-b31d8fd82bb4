'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ChatMessage, ChatRequest } from '@/types/chat';
import { postChatMessage, createChatMessage } from '@/services/api';
import ChatWindow from '@/components/chat/ChatWindow';
import ChatInput from '@/components/chat/ChatInput';
import { Button } from '@/components/ui/button';
import { RefreshCw, MessageCircle } from 'lucide-react';

export default function ChatPage() {
  const { session, user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize chat with welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage = createChatMessage(
        "Hello! I'm your AI Navigator. I can help you find the perfect AI tools, courses, and resources for your needs. What are you looking to accomplish today?",
        'ai'
      );
      setMessages([welcomeMessage]);
    }
  }, [messages.length]);

  const handleSendMessage = async () => {
    if (!userInput.trim() || isLoading) return;

    // Clear any previous errors
    setError(null);

    // Create user message
    const userMessage = createChatMessage(userInput.trim(), 'user');
    const updatedMessages = [...messages, userMessage];
    
    // Update UI immediately for better UX
    setMessages(updatedMessages);
    setUserInput('');
    setIsLoading(true);

    try {
      // Prepare chat request
      const chatRequest: ChatRequest = {
        messages: updatedMessages,
        context: {
          responseStyle: 'detailed',
          maxRecommendations: 6,
        },
      };

      // Send to backend
      const response = await postChatMessage(chatRequest, session?.access_token);
      
      // Add AI response to messages
      setMessages(prevMessages => [...prevMessages, response.message]);
    } catch (error) {
      console.error('Error sending chat message:', error);
      
      // Create error message
      const errorMessage = createChatMessage(
        "I apologize, but I encountered an error while processing your request. Please try again in a moment.",
        'ai'
      );
      
      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearConversation = () => {
    const welcomeMessage = createChatMessage(
      "Hello! I'm your AI Navigator. I can help you find the perfect AI tools, courses, and resources for your needs. What are you looking to accomplish today?",
      'ai'
    );
    setMessages([welcomeMessage]);
    setUserInput('');
    setError(null);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-8rem)] max-w-4xl mx-auto -m-4 bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-3 sm:p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
            <MessageCircle className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-base sm:text-lg font-semibold text-gray-900">AI Navigator Chat</h1>
            <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">
              {user ? `Welcome back, ${user.email?.split('@')[0]}!` : 'Find the perfect AI resources'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearConversation}
            disabled={isLoading}
            className="flex items-center gap-1 sm:gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span className="hidden sm:inline">New Chat</span>
            <span className="sm:hidden">New</span>
          </Button>
        </div>
      </div>

      {/* Error Banner */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">
            <strong>Error:</strong> {error}
          </p>
        </div>
      )}

      {/* Chat Window */}
      <ChatWindow 
        messages={messages} 
        isLoading={isLoading}
      />

      {/* Chat Input */}
      <ChatInput
        userInput={userInput}
        setUserInput={setUserInput}
        onSubmit={handleSendMessage}
        isLoading={isLoading}
        placeholder="Ask me about AI tools, courses, or anything else..."
      />
    </div>
  );
}
